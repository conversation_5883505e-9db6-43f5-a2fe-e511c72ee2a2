package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__137;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysDictType;
import org.dromara.system.domain.SysDictTypeToSysDictTypeVoMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__137.class,
    uses = {SysDictTypeToSysDictTypeVoMapper__2.class},
    imports = {}
)
public interface SysDictTypeVoToSysDictTypeMapper__2 extends BaseMapper<SysDictTypeVo, SysDictType> {
}
