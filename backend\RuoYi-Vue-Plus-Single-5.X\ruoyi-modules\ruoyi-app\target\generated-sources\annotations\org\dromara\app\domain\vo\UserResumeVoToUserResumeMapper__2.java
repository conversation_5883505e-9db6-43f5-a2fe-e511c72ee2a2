package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__137;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.UserResume;
import org.dromara.app.domain.UserResumeToUserResumeVoMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__137.class,
    uses = {UserResumeToUserResumeVoMapper__2.class},
    imports = {}
)
public interface UserResumeVoToUserResumeMapper__2 extends BaseMapper<UserResumeVo, UserResume> {
}
