package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__137;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysOperLog;
import org.dromara.system.domain.SysOperLogToSysOperLogVoMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__137.class,
    uses = {SysOperLogToSysOperLogVoMapper__2.class},
    imports = {}
)
public interface SysOperLogVoToSysOperLogMapper__2 extends BaseMapper<SysOperLogVo, SysOperLog> {
}
