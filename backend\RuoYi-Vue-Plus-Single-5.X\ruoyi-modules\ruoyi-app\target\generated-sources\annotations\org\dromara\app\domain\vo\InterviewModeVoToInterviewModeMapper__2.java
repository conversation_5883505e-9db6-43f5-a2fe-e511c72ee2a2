package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__137;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.InterviewMode;
import org.dromara.app.domain.InterviewModeToInterviewModeVoMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__137.class,
    uses = {InterviewModeToInterviewModeVoMapper__2.class},
    imports = {}
)
public interface InterviewModeVoToInterviewModeMapper__2 extends BaseMapper<InterviewModeVo, InterviewMode> {
}
