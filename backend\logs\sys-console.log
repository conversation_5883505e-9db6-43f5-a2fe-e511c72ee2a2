2025-08-01 15:58:55 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-01 15:58:55 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.10 with PID 24908 (C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes started by lenovo in C:\Users\<USER>\Desktop\softwart-xunfei-code\backend)
2025-08-01 15:58:55 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-01 15:59:21 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-01 15:59:22 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-01 15:59:22 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-01 15:59:41 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@2db0dd19
2025-08-01 15:59:41 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-01 15:59:41 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-01 15:59:41 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-01 15:59:44 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\controller\AuthController.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'sysLoginService' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\service\SysLoginService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysPermissionServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\service\impl\SysPermissionServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\service\impl\SysRoleServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleMapper' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\mapper\SysRoleMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: The alias 'MajorVo' is already mapped to the value 'org.dromara.system.domain.vo.MajorVo'.
2025-08-01 15:59:44 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-01 15:59:44 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-08-01 15:59:57 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-08-01 15:59:57 [main] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-08-01 15:59:57 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-08-01 15:59:57 [main] ERROR o.s.b.c.p.m.PropertiesMigrationListener - 
The use of configuration keys that are no longer supported was found in the environment:

Property source 'Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' (document #10)':
	Key: xunfei.spark.api-key
		Line: 421
		Reason: none
	Key: xunfei.spark.api-secret
		Line: 422
		Reason: none

Property source 'Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' (document #19)':
	Key: xunfei.spark.api-key
		Line: 874
		Reason: none
	Key: xunfei.spark.api-secret
		Line: 875
		Reason: none


Please refer to the release notes or reference guide for potential alternatives.

2025-08-01 15:59:57 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\controller\AuthController.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'sysLoginService' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\service\SysLoginService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysPermissionServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\service\impl\SysPermissionServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\service\impl\SysRoleServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleMapper' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\mapper\SysRoleMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: The alias 'MajorVo' is already mapped to the value 'org.dromara.system.domain.vo.MajorVo'.
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.dromara.DromaraApplication.main(DromaraApplication.java:22)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysLoginService' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\service\SysLoginService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysPermissionServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\service\impl\SysPermissionServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\service\impl\SysRoleServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleMapper' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\mapper\SysRoleMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: The alias 'MajorVo' is already mapped to the value 'org.dromara.system.domain.vo.MajorVo'.
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1740)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysPermissionServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\service\impl\SysPermissionServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\service\impl\SysRoleServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleMapper' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\mapper\SysRoleMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: The alias 'MajorVo' is already mapped to the value 'org.dromara.system.domain.vo.MajorVo'.
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1740)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 33 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysRoleServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\service\impl\SysRoleServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleMapper' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\mapper\SysRoleMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: The alias 'MajorVo' is already mapped to the value 'org.dromara.system.domain.vo.MajorVo'.
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1740)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 47 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysRoleMapper' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\mapper\SysRoleMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: The alias 'MajorVo' is already mapped to the value 'org.dromara.system.domain.vo.MajorVo'.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1556)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1450)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1740)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 61 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: The alias 'MajorVo' is already mapped to the value 'org.dromara.system.domain.vo.MajorVo'.
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:657)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:645)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1205)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1740)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1541)
	... 73 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: The alias 'MajorVo' is already mapped to the value 'org.dromara.system.domain.vo.MajorVo'.
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:199)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiateWithFactoryMethod(SimpleInstantiationStrategy.java:88)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:168)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	... 86 common frames omitted
Caused by: org.apache.ibatis.type.TypeException: The alias 'MajorVo' is already mapped to the value 'org.dromara.system.domain.vo.MajorVo'.
	at org.apache.ibatis.type.TypeAliasRegistry.registerAlias(TypeAliasRegistry.java:166)
	at org.apache.ibatis.type.TypeAliasRegistry.registerAlias(TypeAliasRegistry.java:155)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.HashMap$KeySpliterator.forEachRemaining(HashMap.java:1707)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.buildSqlSessionFactory(MybatisSqlSessionFactoryBean.java:582)
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.afterPropertiesSet(MybatisSqlSessionFactoryBean.java:534)
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.getObject(MybatisSqlSessionFactoryBean.java:692)
	at com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(MybatisPlusAutoConfiguration.java:214)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:171)
	... 89 common frames omitted
2025-08-01 16:46:36 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-01 16:46:36 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.10 with PID 28412 (C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes started by lenovo in C:\Users\<USER>\Desktop\softwart-xunfei-code\backend)
2025-08-01 16:46:36 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-01 16:47:01 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-01 16:47:04 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-01 16:47:04 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-01 16:47:22 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@68b4e7b5
2025-08-01 16:47:22 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-01 16:47:22 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-01 16:47:22 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-01 16:47:26 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\controller\AuthController.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'sysLoginService' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\service\SysLoginService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysPermissionServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\service\impl\SysPermissionServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\service\impl\SysRoleServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleMapper' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\mapper\SysRoleMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: The alias 'PaymentOrderVo' is already mapped to the value 'org.dromara.system.domain.vo.PaymentOrderVo'.
2025-08-01 16:47:26 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-01 16:47:26 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-08-01 16:47:40 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-08-01 16:47:40 [main] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-08-01 16:47:40 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-08-01 16:47:40 [main] ERROR o.s.b.c.p.m.PropertiesMigrationListener - 
The use of configuration keys that are no longer supported was found in the environment:

Property source 'Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' (document #10)':
	Key: xunfei.spark.api-key
		Line: 421
		Reason: none
	Key: xunfei.spark.api-secret
		Line: 422
		Reason: none

Property source 'Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' (document #19)':
	Key: xunfei.spark.api-key
		Line: 874
		Reason: none
	Key: xunfei.spark.api-secret
		Line: 875
		Reason: none


Please refer to the release notes or reference guide for potential alternatives.

2025-08-01 16:47:40 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\controller\AuthController.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'sysLoginService' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\service\SysLoginService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysPermissionServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\service\impl\SysPermissionServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\service\impl\SysRoleServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleMapper' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\mapper\SysRoleMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: The alias 'PaymentOrderVo' is already mapped to the value 'org.dromara.system.domain.vo.PaymentOrderVo'.
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.dromara.DromaraApplication.main(DromaraApplication.java:22)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysLoginService' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\service\SysLoginService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysPermissionServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\service\impl\SysPermissionServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\service\impl\SysRoleServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleMapper' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\mapper\SysRoleMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: The alias 'PaymentOrderVo' is already mapped to the value 'org.dromara.system.domain.vo.PaymentOrderVo'.
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1740)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysPermissionServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\service\impl\SysPermissionServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\service\impl\SysRoleServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleMapper' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\mapper\SysRoleMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: The alias 'PaymentOrderVo' is already mapped to the value 'org.dromara.system.domain.vo.PaymentOrderVo'.
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1740)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 33 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysRoleServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\service\impl\SysRoleServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleMapper' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\mapper\SysRoleMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: The alias 'PaymentOrderVo' is already mapped to the value 'org.dromara.system.domain.vo.PaymentOrderVo'.
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1740)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 47 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysRoleMapper' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\ruoyi-system\target\classes\org\dromara\system\mapper\SysRoleMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: The alias 'PaymentOrderVo' is already mapped to the value 'org.dromara.system.domain.vo.PaymentOrderVo'.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1556)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1450)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1740)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 61 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: The alias 'PaymentOrderVo' is already mapped to the value 'org.dromara.system.domain.vo.PaymentOrderVo'.
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:657)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:645)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1205)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1740)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1541)
	... 73 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: The alias 'PaymentOrderVo' is already mapped to the value 'org.dromara.system.domain.vo.PaymentOrderVo'.
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:199)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiateWithFactoryMethod(SimpleInstantiationStrategy.java:88)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:168)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	... 86 common frames omitted
Caused by: org.apache.ibatis.type.TypeException: The alias 'PaymentOrderVo' is already mapped to the value 'org.dromara.system.domain.vo.PaymentOrderVo'.
	at org.apache.ibatis.type.TypeAliasRegistry.registerAlias(TypeAliasRegistry.java:166)
	at org.apache.ibatis.type.TypeAliasRegistry.registerAlias(TypeAliasRegistry.java:155)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.HashMap$KeySpliterator.forEachRemaining(HashMap.java:1707)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.buildSqlSessionFactory(MybatisSqlSessionFactoryBean.java:582)
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.afterPropertiesSet(MybatisSqlSessionFactoryBean.java:534)
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.getObject(MybatisSqlSessionFactoryBean.java:692)
	at com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(MybatisPlusAutoConfiguration.java:214)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:171)
	... 89 common frames omitted
2025-08-01 17:28:15 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-01 17:28:15 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.10 with PID 35136 (C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes started by lenovo in C:\Users\<USER>\Desktop\softwart-xunfei-code\backend)
2025-08-01 17:28:15 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-01 17:28:26 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-08-01 17:28:26 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-01 17:28:26 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\controller\AuthController.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'sysLoginService' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\service\SysLoginService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysPermissionServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\org\dromara\system\service\impl\SysPermissionServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\org\dromara\system\service\impl\SysRoleServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'org.dromara.system.mapper.SysRoleMapper' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-08-01 17:28:26 [main] ERROR o.s.b.c.p.m.PropertiesMigrationListener - 
The use of configuration keys that are no longer supported was found in the environment:

Property source 'Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' (document #10)':
	Key: xunfei.spark.api-key
		Line: 421
		Reason: none
	Key: xunfei.spark.api-secret
		Line: 422
		Reason: none

Property source 'Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' (document #19)':
	Key: xunfei.spark.api-key
		Line: 874
		Reason: none
	Key: xunfei.spark.api-secret
		Line: 875
		Reason: none


Please refer to the release notes or reference guide for potential alternatives.

2025-08-01 17:28:27 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\controller\AuthController.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'sysLoginService' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\service\SysLoginService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysPermissionServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\org\dromara\system\service\impl\SysPermissionServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\org\dromara\system\service\impl\SysRoleServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'org.dromara.system.mapper.SysRoleMapper' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.dromara.DromaraApplication.main(DromaraApplication.java:22)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysLoginService' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\service\SysLoginService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysPermissionServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\org\dromara\system\service\impl\SysPermissionServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\org\dromara\system\service\impl\SysRoleServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'org.dromara.system.mapper.SysRoleMapper' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1740)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysPermissionServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\org\dromara\system\service\impl\SysPermissionServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\org\dromara\system\service\impl\SysRoleServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'org.dromara.system.mapper.SysRoleMapper' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1740)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 33 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysRoleServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\org\dromara\system\service\impl\SysRoleServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'org.dromara.system.mapper.SysRoleMapper' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1740)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 47 common frames omitted
Caused by: org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'org.dromara.system.mapper.SysRoleMapper' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.raiseNoMatchingBeanFound(DefaultListableBeanFactory.java:2280)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1703)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 61 common frames omitted
2025-08-01 17:29:10 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-01 17:29:10 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.10 with PID 8392 (C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes started by lenovo in C:\Users\<USER>\Desktop\softwart-xunfei-code\backend)
2025-08-01 17:29:10 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-01 17:29:19 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-08-01 17:29:19 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-01 17:29:19 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\controller\AuthController.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'sysLoginService' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\service\SysLoginService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysPermissionServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\org\dromara\system\service\impl\SysPermissionServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\org\dromara\system\service\impl\SysRoleServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'org.dromara.system.mapper.SysRoleMapper' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-08-01 17:29:19 [main] ERROR o.s.b.c.p.m.PropertiesMigrationListener - 
The use of configuration keys that are no longer supported was found in the environment:

Property source 'Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' (document #10)':
	Key: xunfei.spark.api-key
		Line: 421
		Reason: none
	Key: xunfei.spark.api-secret
		Line: 422
		Reason: none

Property source 'Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' (document #19)':
	Key: xunfei.spark.api-key
		Line: 874
		Reason: none
	Key: xunfei.spark.api-secret
		Line: 875
		Reason: none


Please refer to the release notes or reference guide for potential alternatives.

2025-08-01 17:29:19 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\controller\AuthController.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'sysLoginService' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\service\SysLoginService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysPermissionServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\org\dromara\system\service\impl\SysPermissionServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\org\dromara\system\service\impl\SysRoleServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'org.dromara.system.mapper.SysRoleMapper' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.dromara.DromaraApplication.main(DromaraApplication.java:22)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysLoginService' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\service\SysLoginService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysPermissionServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\org\dromara\system\service\impl\SysPermissionServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\org\dromara\system\service\impl\SysRoleServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'org.dromara.system.mapper.SysRoleMapper' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1740)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysPermissionServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\org\dromara\system\service\impl\SysPermissionServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\org\dromara\system\service\impl\SysRoleServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'org.dromara.system.mapper.SysRoleMapper' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1740)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 33 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysRoleServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\org\dromara\system\service\impl\SysRoleServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'org.dromara.system.mapper.SysRoleMapper' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1740)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 47 common frames omitted
Caused by: org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'org.dromara.system.mapper.SysRoleMapper' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.raiseNoMatchingBeanFound(DefaultListableBeanFactory.java:2280)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1703)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 61 common frames omitted
2025-08-01 17:39:39 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-01 17:39:39 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.10 with PID 4876 (C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes started by lenovo in C:\Users\<USER>\Desktop\softwart-xunfei-code\backend)
2025-08-01 17:39:39 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-01 17:39:49 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-08-01 17:39:49 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-01 17:39:50 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-01 17:39:50 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-01 17:40:08 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@53bc6492
2025-08-01 17:40:08 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-01 17:40:08 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-01 17:40:08 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-01 17:40:09 [main] WARN  c.b.m.c.injector.methods.SelectByIds - [org.dromara.app.mapper.BadgeMapper.selectByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectByIds]
2025-08-01 17:40:09 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.searchMessages] is ignored, because it exists, maybe from xml file
2025-08-01 17:40:09 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.getMessageStats] is ignored, because it exists, maybe from xml file
2025-08-01 17:40:09 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.selectRecentMessages] is ignored, because it exists, maybe from xml file
2025-08-01 17:40:09 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.countSessionMessages] is ignored, because it exists, maybe from xml file
2025-08-01 17:40:09 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.countUserSessions] is ignored, because it exists, maybe from xml file
2025-08-01 17:40:09 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectSessionStats] is ignored, because it exists, maybe from xml file
2025-08-01 17:40:09 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectActiveSessions] is ignored, because it exists, maybe from xml file
2025-08-01 17:40:09 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectUserSessionsByAgent] is ignored, because it exists, maybe from xml file
2025-08-01 17:40:09 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.getPopularAgentTypes] is ignored, because it exists, maybe from xml file
2025-08-01 17:40:09 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectRecentSessions] is ignored, because it exists, maybe from xml file
2025-08-01 17:40:10 [main] WARN  c.b.m.c.injector.methods.SelectByIds - [org.dromara.app.mapper.InterviewQuestionMapper.selectByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectByIds]
2025-08-01 17:40:10 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ToolCallMapper.getUserToolStats] is ignored, because it exists, maybe from xml file
2025-08-01 17:40:11 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\controller\AuthController.class]: Unsatisfied dependency expressed through constructor parameter 2: Error creating bean with name 'sysRegisterService' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\service\SysRegisterService.class]: Unsatisfied dependency expressed through constructor parameter 2: No qualifying bean of type 'org.dromara.common.web.config.properties.CaptchaProperties' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-08-01 17:40:11 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-01 17:40:11 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-08-01 17:40:23 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-08-01 17:40:23 [main] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-08-01 17:40:23 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-08-01 17:40:23 [main] ERROR o.s.b.c.p.m.PropertiesMigrationListener - 
The use of configuration keys that are no longer supported was found in the environment:

Property source 'Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' (document #10)':
	Key: xunfei.spark.api-key
		Line: 421
		Reason: none
	Key: xunfei.spark.api-secret
		Line: 422
		Reason: none

Property source 'Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' (document #19)':
	Key: xunfei.spark.api-key
		Line: 874
		Reason: none
	Key: xunfei.spark.api-secret
		Line: 875
		Reason: none


Please refer to the release notes or reference guide for potential alternatives.

2025-08-01 17:40:23 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\controller\AuthController.class]: Unsatisfied dependency expressed through constructor parameter 2: Error creating bean with name 'sysRegisterService' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\service\SysRegisterService.class]: Unsatisfied dependency expressed through constructor parameter 2: No qualifying bean of type 'org.dromara.common.web.config.properties.CaptchaProperties' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.dromara.DromaraApplication.main(DromaraApplication.java:24)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysRegisterService' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\service\SysRegisterService.class]: Unsatisfied dependency expressed through constructor parameter 2: No qualifying bean of type 'org.dromara.common.web.config.properties.CaptchaProperties' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1740)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'org.dromara.common.web.config.properties.CaptchaProperties' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.raiseNoMatchingBeanFound(DefaultListableBeanFactory.java:2280)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1703)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 33 common frames omitted
2025-08-01 20:48:06 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-01 20:48:06 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.10 with PID 17496 (C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes started by lenovo in C:\Users\<USER>\Desktop\softwart-xunfei-code\backend)
2025-08-01 20:48:06 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-01 20:48:19 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-08-01 20:48:19 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-01 20:48:21 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-01 20:48:21 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-01 20:48:39 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@119f035f
2025-08-01 20:48:39 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-01 20:48:39 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-01 20:48:39 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-01 20:48:41 [main] WARN  c.b.m.c.injector.methods.SelectByIds - [org.dromara.app.mapper.BadgeMapper.selectByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectByIds]
2025-08-01 20:48:41 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.searchMessages] is ignored, because it exists, maybe from xml file
2025-08-01 20:48:41 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.getMessageStats] is ignored, because it exists, maybe from xml file
2025-08-01 20:48:41 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.selectRecentMessages] is ignored, because it exists, maybe from xml file
2025-08-01 20:48:41 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.countSessionMessages] is ignored, because it exists, maybe from xml file
2025-08-01 20:48:41 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectActiveSessions] is ignored, because it exists, maybe from xml file
2025-08-01 20:48:41 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.getPopularAgentTypes] is ignored, because it exists, maybe from xml file
2025-08-01 20:48:41 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectUserSessionsByAgent] is ignored, because it exists, maybe from xml file
2025-08-01 20:48:41 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectRecentSessions] is ignored, because it exists, maybe from xml file
2025-08-01 20:48:41 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.countUserSessions] is ignored, because it exists, maybe from xml file
2025-08-01 20:48:41 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectSessionStats] is ignored, because it exists, maybe from xml file
2025-08-01 20:48:41 [main] WARN  c.b.m.c.injector.methods.SelectByIds - [org.dromara.app.mapper.InterviewQuestionMapper.selectByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectByIds]
2025-08-01 20:48:42 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ToolCallMapper.getUserToolStats] is ignored, because it exists, maybe from xml file
2025-08-01 20:48:44 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\controller\AuthController.class]: Unsatisfied dependency expressed through constructor parameter 2: Error creating bean with name 'sysRegisterService' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\service\SysRegisterService.class]: Unsatisfied dependency expressed through constructor parameter 2: No qualifying bean of type 'org.dromara.common.web.config.properties.CaptchaProperties' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-08-01 20:48:44 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-01 20:48:44 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-08-01 20:48:55 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-08-01 20:48:55 [main] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-08-01 20:48:55 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-08-01 20:48:55 [main] ERROR o.s.b.c.p.m.PropertiesMigrationListener - 
The use of configuration keys that are no longer supported was found in the environment:

Property source 'Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' (document #10)':
	Key: xunfei.spark.api-key
		Line: 421
		Reason: none
	Key: xunfei.spark.api-secret
		Line: 422
		Reason: none

Property source 'Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' (document #19)':
	Key: xunfei.spark.api-key
		Line: 874
		Reason: none
	Key: xunfei.spark.api-secret
		Line: 875
		Reason: none


Please refer to the release notes or reference guide for potential alternatives.

2025-08-01 20:48:55 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\controller\AuthController.class]: Unsatisfied dependency expressed through constructor parameter 2: Error creating bean with name 'sysRegisterService' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\service\SysRegisterService.class]: Unsatisfied dependency expressed through constructor parameter 2: No qualifying bean of type 'org.dromara.common.web.config.properties.CaptchaProperties' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.dromara.DromaraApplication.main(DromaraApplication.java:24)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysRegisterService' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\service\SysRegisterService.class]: Unsatisfied dependency expressed through constructor parameter 2: No qualifying bean of type 'org.dromara.common.web.config.properties.CaptchaProperties' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1740)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'org.dromara.common.web.config.properties.CaptchaProperties' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.raiseNoMatchingBeanFound(DefaultListableBeanFactory.java:2280)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1703)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 33 common frames omitted
2025-08-01 21:11:59 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-01 21:11:59 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.10 with PID 13424 (C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes started by lenovo in C:\Users\<USER>\Desktop\softwart-xunfei-code\backend)
2025-08-01 21:11:59 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-01 21:12:10 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-08-01 21:12:10 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-01 21:12:10 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-01 21:12:11 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-01 21:12:27 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@7053a864
2025-08-01 21:12:27 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-01 21:12:27 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-01 21:12:27 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-01 21:12:29 [main] WARN  c.b.m.c.injector.methods.SelectByIds - [org.dromara.app.mapper.BadgeMapper.selectByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectByIds]
2025-08-01 21:12:29 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.searchMessages] is ignored, because it exists, maybe from xml file
2025-08-01 21:12:29 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.getMessageStats] is ignored, because it exists, maybe from xml file
2025-08-01 21:12:29 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.selectRecentMessages] is ignored, because it exists, maybe from xml file
2025-08-01 21:12:29 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.countSessionMessages] is ignored, because it exists, maybe from xml file
2025-08-01 21:12:29 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectSessionStats] is ignored, because it exists, maybe from xml file
2025-08-01 21:12:29 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectRecentSessions] is ignored, because it exists, maybe from xml file
2025-08-01 21:12:29 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectUserSessionsByAgent] is ignored, because it exists, maybe from xml file
2025-08-01 21:12:29 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.getPopularAgentTypes] is ignored, because it exists, maybe from xml file
2025-08-01 21:12:29 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectActiveSessions] is ignored, because it exists, maybe from xml file
2025-08-01 21:12:29 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.countUserSessions] is ignored, because it exists, maybe from xml file
2025-08-01 21:12:29 [main] WARN  c.b.m.c.injector.methods.SelectByIds - [org.dromara.app.mapper.InterviewQuestionMapper.selectByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectByIds]
2025-08-01 21:12:29 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\controller\AuthController.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'sysLoginService' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\service\SysLoginService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysPermissionServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\org\dromara\system\service\impl\SysPermissionServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\org\dromara\system\service\impl\SysRoleServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleMapper' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\org\dromara\system\mapper\SysRoleMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\mapper\app\InterviewResultMapper.xml]'
2025-08-01 21:12:29 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-01 21:12:29 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-08-01 21:12:43 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-08-01 21:12:43 [main] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-08-01 21:12:43 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-08-01 21:12:43 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\controller\AuthController.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'sysLoginService' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\service\SysLoginService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysPermissionServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\org\dromara\system\service\impl\SysPermissionServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\org\dromara\system\service\impl\SysRoleServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleMapper' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\org\dromara\system\mapper\SysRoleMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\mapper\app\InterviewResultMapper.xml]'
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.dromara.DromaraApplication.main(DromaraApplication.java:25)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysLoginService' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\service\SysLoginService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysPermissionServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\org\dromara\system\service\impl\SysPermissionServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\org\dromara\system\service\impl\SysRoleServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleMapper' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\org\dromara\system\mapper\SysRoleMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\mapper\app\InterviewResultMapper.xml]'
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1740)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysPermissionServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\org\dromara\system\service\impl\SysPermissionServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\org\dromara\system\service\impl\SysRoleServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleMapper' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\org\dromara\system\mapper\SysRoleMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\mapper\app\InterviewResultMapper.xml]'
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1740)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 33 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysRoleServiceImpl' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\org\dromara\system\service\impl\SysRoleServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'sysRoleMapper' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\org\dromara\system\mapper\SysRoleMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\mapper\app\InterviewResultMapper.xml]'
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1740)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 47 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysRoleMapper' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\org\dromara\system\mapper\SysRoleMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\mapper\app\InterviewResultMapper.xml]'
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1556)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1450)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1740)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 61 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\mapper\app\InterviewResultMapper.xml]'
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:657)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:645)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1205)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1740)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1541)
	... 73 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\mapper\app\InterviewResultMapper.xml]'
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:199)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiateWithFactoryMethod(SimpleInstantiationStrategy.java:88)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:168)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	... 86 common frames omitted
Caused by: java.io.IOException: Failed to parse mapping resource: 'file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\mapper\app\InterviewResultMapper.xml]'
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.buildSqlSessionFactory(MybatisSqlSessionFactoryBean.java:661)
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.afterPropertiesSet(MybatisSqlSessionFactoryBean.java:534)
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.getObject(MybatisSqlSessionFactoryBean.java:692)
	at com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(MybatisPlusAutoConfiguration.java:214)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:171)
	... 89 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-modules\target\classes\mapper\app\InterviewResultMapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'org.dromara.common.json.handler.JsonStringListTypeHandler'.  Cause: java.lang.ClassNotFoundException: Cannot find class: org.dromara.common.json.handler.JsonStringListTypeHandler
	at com.baomidou.mybatisplus.core.MybatisXMLMapperBuilder.configurationElement(MybatisXMLMapperBuilder.java:129)
	at com.baomidou.mybatisplus.core.MybatisXMLMapperBuilder.parse(MybatisXMLMapperBuilder.java:102)
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.buildSqlSessionFactory(MybatisSqlSessionFactoryBean.java:659)
	... 97 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'org.dromara.common.json.handler.JsonStringListTypeHandler'.  Cause: java.lang.ClassNotFoundException: Cannot find class: org.dromara.common.json.handler.JsonStringListTypeHandler
	at org.apache.ibatis.builder.BaseBuilder.resolveClass(BaseBuilder.java:103)
	at com.baomidou.mybatisplus.core.MybatisXMLMapperBuilder.buildResultMappingFromContext(MybatisXMLMapperBuilder.java:358)
	at com.baomidou.mybatisplus.core.MybatisXMLMapperBuilder.resultMapElement(MybatisXMLMapperBuilder.java:243)
	at com.baomidou.mybatisplus.core.MybatisXMLMapperBuilder.resultMapElement(MybatisXMLMapperBuilder.java:218)
	at com.baomidou.mybatisplus.core.MybatisXMLMapperBuilder.resultMapElements(MybatisXMLMapperBuilder.java:210)
	at com.baomidou.mybatisplus.core.MybatisXMLMapperBuilder.configurationElement(MybatisXMLMapperBuilder.java:125)
	... 99 common frames omitted
Caused by: org.apache.ibatis.type.TypeException: Could not resolve type alias 'org.dromara.common.json.handler.JsonStringListTypeHandler'.  Cause: java.lang.ClassNotFoundException: Cannot find class: org.dromara.common.json.handler.JsonStringListTypeHandler
	at org.apache.ibatis.type.TypeAliasRegistry.resolveAlias(TypeAliasRegistry.java:128)
	at org.apache.ibatis.builder.BaseBuilder.resolveAlias(BaseBuilder.java:132)
	at org.apache.ibatis.builder.BaseBuilder.resolveClass(BaseBuilder.java:101)
	... 104 common frames omitted
Caused by: java.lang.ClassNotFoundException: Cannot find class: org.dromara.common.json.handler.JsonStringListTypeHandler
	at org.apache.ibatis.io.ClassLoaderWrapper.classForName(ClassLoaderWrapper.java:226)
	at org.apache.ibatis.io.ClassLoaderWrapper.classForName(ClassLoaderWrapper.java:103)
	at org.apache.ibatis.io.Resources.classForName(Resources.java:322)
	at org.apache.ibatis.type.TypeAliasRegistry.resolveAlias(TypeAliasRegistry.java:124)
	... 106 common frames omitted
2025-08-01 21:22:17 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-01 21:22:17 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.10 with PID 29872 (C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes started by lenovo in C:\Users\<USER>\Desktop\softwart-xunfei-code\backend)
2025-08-01 21:22:17 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-01 21:22:41 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-08-01 21:22:41 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-01 21:22:42 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-01 21:22:42 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-01 21:23:01 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@53bc6492
2025-08-01 21:23:01 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-01 21:23:01 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-01 21:23:01 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-01 21:23:03 [main] WARN  c.b.m.c.injector.methods.SelectByIds - [org.dromara.app.mapper.BadgeMapper.selectByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectByIds]
2025-08-01 21:23:03 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.searchMessages] is ignored, because it exists, maybe from xml file
2025-08-01 21:23:03 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.getMessageStats] is ignored, because it exists, maybe from xml file
2025-08-01 21:23:03 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.selectRecentMessages] is ignored, because it exists, maybe from xml file
2025-08-01 21:23:03 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.countSessionMessages] is ignored, because it exists, maybe from xml file
2025-08-01 21:23:03 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.countUserSessions] is ignored, because it exists, maybe from xml file
2025-08-01 21:23:03 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectActiveSessions] is ignored, because it exists, maybe from xml file
2025-08-01 21:23:03 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectRecentSessions] is ignored, because it exists, maybe from xml file
2025-08-01 21:23:03 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.getPopularAgentTypes] is ignored, because it exists, maybe from xml file
2025-08-01 21:23:03 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectUserSessionsByAgent] is ignored, because it exists, maybe from xml file
2025-08-01 21:23:03 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectSessionStats] is ignored, because it exists, maybe from xml file
2025-08-01 21:23:03 [main] WARN  c.b.m.c.injector.methods.SelectByIds - [org.dromara.app.mapper.InterviewQuestionMapper.selectByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectByIds]
2025-08-01 21:23:04 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ToolCallMapper.getUserToolStats] is ignored, because it exists, maybe from xml file
2025-08-01 21:23:06 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\controller\AuthController.class]: Unsatisfied dependency expressed through constructor parameter 6: No qualifying bean of type 'java.util.concurrent.ScheduledExecutorService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-08-01 21:23:06 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-01 21:23:06 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-08-01 21:23:19 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-08-01 21:23:19 [main] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-08-01 21:23:19 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-08-01 21:23:19 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\controller\AuthController.class]: Unsatisfied dependency expressed through constructor parameter 6: No qualifying bean of type 'java.util.concurrent.ScheduledExecutorService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.dromara.DromaraApplication.main(DromaraApplication.java:25)
Caused by: org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'java.util.concurrent.ScheduledExecutorService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.raiseNoMatchingBeanFound(DefaultListableBeanFactory.java:2280)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1703)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 19 common frames omitted
2025-08-01 21:33:41 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-01 21:33:41 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.10 with PID 9592 (C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes started by lenovo in C:\Users\<USER>\Desktop\softwart-xunfei-code\backend)
2025-08-01 21:33:41 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-01 21:33:53 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-08-01 21:33:53 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-01 21:33:54 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'authController': Lookup method resolution failed
2025-08-01 21:33:54 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'authController': Lookup method resolution failed
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.checkLookupMethods(AutowiredAnnotationBeanPostProcessor.java:498)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.determineCandidateConstructors(AutowiredAnnotationBeanPostProcessor.java:368)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.determineConstructorsFromBeanPostProcessors(AbstractAutowireCapableBeanFactory.java:1334)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1229)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.dromara.DromaraApplication.main(DromaraApplication.java:25)
Caused by: java.lang.IllegalStateException: Failed to introspect Class [org.dromara.web.controller.AuthController] from ClassLoader [jdk.internal.loader.ClassLoaders$AppClassLoader@63947c6b]
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:483)
	at org.springframework.util.ReflectionUtils.doWithLocalMethods(ReflectionUtils.java:320)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.checkLookupMethods(AutowiredAnnotationBeanPostProcessor.java:476)
	... 19 common frames omitted
Caused by: java.lang.NoClassDefFoundError: RegisterBody
	at java.base/java.lang.Class.getDeclaredMethods0(Native Method)
	at java.base/java.lang.Class.privateGetDeclaredMethods(Class.java:3402)
	at java.base/java.lang.Class.getDeclaredMethods(Class.java:2504)
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:465)
	... 21 common frames omitted
Caused by: java.lang.ClassNotFoundException: RegisterBody
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641)
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:520)
	... 25 common frames omitted
