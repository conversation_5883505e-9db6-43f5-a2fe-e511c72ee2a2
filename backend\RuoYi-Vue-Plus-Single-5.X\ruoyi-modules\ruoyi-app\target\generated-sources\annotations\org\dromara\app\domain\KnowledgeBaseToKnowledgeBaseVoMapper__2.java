package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__137;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.KnowledgeBaseBoToKnowledgeBaseMapper__2;
import org.dromara.app.domain.vo.KnowledgeBaseVo;
import org.dromara.app.domain.vo.KnowledgeBaseVoToKnowledgeBaseMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__137.class,
    uses = {KnowledgeBaseBoToKnowledgeBaseMapper__2.class,KnowledgeBaseVoToKnowledgeBaseMapper__2.class},
    imports = {}
)
public interface KnowledgeBaseToKnowledgeBaseVoMapper__2 extends BaseMapper<KnowledgeBase, KnowledgeBaseVo> {
}
