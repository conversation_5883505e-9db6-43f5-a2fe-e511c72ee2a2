{"doc": "\n 认证\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "login", "paramTypes": ["java.lang.String"], "doc": "\n 登录方法\r\n\r\n @param body 登录信息\r\n @return 结果\r\n"}, {"name": "auth<PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 获取跳转URL\r\n\r\n @param source 登录来源\r\n @return 结果\r\n"}, {"name": "socialCallback", "paramTypes": ["SocialLoginBody"], "doc": "\n 前端回调绑定授权(需要token)\r\n\r\n @param loginBody 请求体\r\n @return 结果\r\n"}, {"name": "unlockSocial", "paramTypes": ["java.lang.Long"], "doc": "\n 取消授权(需要token)\r\n\r\n @param socialId socialId\r\n"}, {"name": "logout", "paramTypes": [], "doc": "\n 退出登录\r\n"}, {"name": "register", "paramTypes": ["RegisterBody"], "doc": "\n 用户注册\r\n"}, {"name": "tenantList", "paramTypes": [], "doc": "\n 登录页面租户下拉框（兼容多租户前端）\r\n"}], "constructors": []}