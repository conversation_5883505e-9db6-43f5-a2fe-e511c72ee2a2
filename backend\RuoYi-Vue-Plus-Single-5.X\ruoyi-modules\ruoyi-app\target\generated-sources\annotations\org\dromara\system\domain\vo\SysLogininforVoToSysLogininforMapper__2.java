package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__137;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysLogininfor;
import org.dromara.system.domain.SysLogininforToSysLogininforVoMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__137.class,
    uses = {SysLogininforToSysLogininforVoMapper__2.class},
    imports = {}
)
public interface SysLogininforVoToSysLogininforMapper__2 extends BaseMapper<SysLogininforVo, SysLogininfor> {
}
