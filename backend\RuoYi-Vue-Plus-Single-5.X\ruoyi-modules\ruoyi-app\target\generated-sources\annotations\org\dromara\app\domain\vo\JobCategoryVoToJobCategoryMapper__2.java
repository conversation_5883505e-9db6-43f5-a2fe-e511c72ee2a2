package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__137;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.JobCategory;
import org.dromara.app.domain.JobCategoryToJobCategoryVoMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__137.class,
    uses = {JobCategoryToJobCategoryVoMapper__2.class},
    imports = {}
)
public interface JobCategoryVoToJobCategoryMapper__2 extends BaseMapper<JobCategoryVo, JobCategory> {
}
