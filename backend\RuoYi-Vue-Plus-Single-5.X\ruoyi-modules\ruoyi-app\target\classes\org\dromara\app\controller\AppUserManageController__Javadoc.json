{"doc": " 应用用户管理\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.app.domain.bo.AppUserManageBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询应用用户列表\n"}, {"name": "export", "paramTypes": ["org.dromara.app.domain.bo.AppUserManageBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出应用用户列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取应用用户详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.app.domain.bo.AppUserManageBo"], "doc": " 新增应用用户\n"}, {"name": "edit", "paramTypes": ["org.dromara.app.domain.bo.AppUserManageBo"], "doc": " 修改应用用户\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除应用用户\n"}, {"name": "resetPwd", "paramTypes": ["org.dromara.app.domain.bo.AppUserManageBo"], "doc": " 重置用户密码\n"}, {"name": "changeStatus", "paramTypes": ["org.dromara.app.domain.bo.AppUserManageBo"], "doc": " 状态修改\n"}], "constructors": []}