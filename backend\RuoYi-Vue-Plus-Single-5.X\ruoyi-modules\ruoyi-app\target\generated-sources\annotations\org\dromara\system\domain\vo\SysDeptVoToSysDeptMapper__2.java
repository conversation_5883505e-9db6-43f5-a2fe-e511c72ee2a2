package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__137;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysDept;
import org.dromara.system.domain.SysDeptToSysDeptVoMapper__2;
import org.dromara.system.domain.bo.SysDeptBoToSysDeptMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__137.class,
    uses = {SysDeptBoToSysDeptMapper__2.class,SysDeptToSysDeptVoMapper__2.class,SysDeptToSysDeptVoMapper__2.class},
    imports = {}
)
public interface SysDeptVoToSysDeptMapper__2 extends BaseMapper<SysDeptVo, SysDept> {
}
