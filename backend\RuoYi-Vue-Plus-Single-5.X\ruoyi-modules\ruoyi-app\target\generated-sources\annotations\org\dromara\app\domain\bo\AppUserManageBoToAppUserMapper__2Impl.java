package org.dromara.app.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.app.domain.AppUser;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T21:25:55+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class AppUserManageBoToAppUserMapper__2Impl implements AppUserManageBoToAppUserMapper__2 {

    @Override
    public AppUser convert(AppUserManageBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppUser appUser = new AppUser();

        appUser.setCreateBy( arg0.getCreateBy() );
        appUser.setCreateDept( arg0.getCreateDept() );
        appUser.setCreateTime( arg0.getCreateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            appUser.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        appUser.setSearchValue( arg0.getSearchValue() );
        appUser.setUpdateBy( arg0.getUpdateBy() );
        appUser.setUpdateTime( arg0.getUpdateTime() );
        appUser.setAvatar( arg0.getAvatar() );
        appUser.setDelFlag( arg0.getDelFlag() );
        appUser.setEmail( arg0.getEmail() );
        appUser.setGender( arg0.getGender() );
        appUser.setGrade( arg0.getGrade() );
        appUser.setIntroduction( arg0.getIntroduction() );
        appUser.setLoginDate( arg0.getLoginDate() );
        appUser.setLoginIp( arg0.getLoginIp() );
        appUser.setMajor( arg0.getMajor() );
        appUser.setPassword( arg0.getPassword() );
        appUser.setPhone( arg0.getPhone() );
        appUser.setRealName( arg0.getRealName() );
        appUser.setRegisteredAt( arg0.getRegisteredAt() );
        appUser.setRemark( arg0.getRemark() );
        appUser.setSchool( arg0.getSchool() );
        appUser.setStatus( arg0.getStatus() );
        appUser.setStudentId( arg0.getStudentId() );
        appUser.setUserId( arg0.getUserId() );

        return appUser;
    }

    @Override
    public AppUser convert(AppUserManageBo arg0, AppUser arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateTime( arg0.getCreateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setAvatar( arg0.getAvatar() );
        arg1.setDelFlag( arg0.getDelFlag() );
        arg1.setEmail( arg0.getEmail() );
        arg1.setGender( arg0.getGender() );
        arg1.setGrade( arg0.getGrade() );
        arg1.setIntroduction( arg0.getIntroduction() );
        arg1.setLoginDate( arg0.getLoginDate() );
        arg1.setLoginIp( arg0.getLoginIp() );
        arg1.setMajor( arg0.getMajor() );
        arg1.setPassword( arg0.getPassword() );
        arg1.setPhone( arg0.getPhone() );
        arg1.setRealName( arg0.getRealName() );
        arg1.setRegisteredAt( arg0.getRegisteredAt() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setSchool( arg0.getSchool() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setStudentId( arg0.getStudentId() );
        arg1.setUserId( arg0.getUserId() );

        return arg1;
    }
}
