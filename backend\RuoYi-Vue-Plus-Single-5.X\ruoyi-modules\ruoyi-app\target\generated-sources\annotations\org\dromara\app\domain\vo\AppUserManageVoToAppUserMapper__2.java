package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__137;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.AppUser;
import org.dromara.app.domain.AppUserToAppUserManageVoMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__137.class,
    uses = {AppUserToAppUserManageVoMapper__2.class},
    imports = {}
)
public interface AppUserManageVoToAppUserMapper__2 extends BaseMapper<AppUserManageVo, AppUser> {
}
