package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__137;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysMenu;
import org.dromara.system.domain.SysMenuToSysMenuVoMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__137.class,
    uses = {SysMenuToSysMenuVoMapper__2.class,SysMenuToSysMenuVoMapper__2.class},
    imports = {}
)
public interface SysMenuVoToSysMenuMapper__2 extends BaseMapper<SysMenuVo, SysMenu> {
}
