package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__137;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysDictDataBoToSysDictDataMapper__2;
import org.dromara.system.domain.vo.SysDictDataVo;
import org.dromara.system.domain.vo.SysDictDataVoToSysDictDataMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__137.class,
    uses = {SysDictDataBoToSysDictDataMapper__2.class,SysDictDataVoToSysDictDataMapper__2.class},
    imports = {}
)
public interface SysDictDataToSysDictDataVoMapper__2 extends BaseMapper<SysDictData, SysDictDataVo> {
}
