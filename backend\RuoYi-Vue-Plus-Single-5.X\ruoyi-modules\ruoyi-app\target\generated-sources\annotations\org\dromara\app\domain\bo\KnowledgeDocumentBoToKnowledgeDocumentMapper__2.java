package org.dromara.app.domain.bo;

import io.github.linpeilie.AutoMapperConfig__137;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.KnowledgeDocument;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__137.class,
    uses = {},
    imports = {}
)
public interface KnowledgeDocumentBoToKnowledgeDocumentMapper__2 extends BaseMapper<KnowledgeDocumentBo, KnowledgeDocument> {
}
