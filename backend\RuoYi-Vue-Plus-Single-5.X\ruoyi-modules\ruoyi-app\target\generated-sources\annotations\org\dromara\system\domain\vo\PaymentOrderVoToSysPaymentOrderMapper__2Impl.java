package org.dromara.system.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.system.domain.SysPaymentOrder;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T21:25:55+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class PaymentOrderVoToSysPaymentOrderMapper__2Impl implements PaymentOrderVoToSysPaymentOrderMapper__2 {

    @Override
    public SysPaymentOrder convert(PaymentOrderVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysPaymentOrder sysPaymentOrder = new SysPaymentOrder();

        sysPaymentOrder.setCreateTime( arg0.getCreateTime() );
        sysPaymentOrder.setUpdateTime( arg0.getUpdateTime() );
        sysPaymentOrder.setAlipayTradeNo( arg0.getAlipayTradeNo() );
        sysPaymentOrder.setAmount( arg0.getAmount() );
        sysPaymentOrder.setClientIp( arg0.getClientIp() );
        sysPaymentOrder.setExpireTime( arg0.getExpireTime() );
        sysPaymentOrder.setLastNotifyTime( arg0.getLastNotifyTime() );
        sysPaymentOrder.setNotifyCount( arg0.getNotifyCount() );
        sysPaymentOrder.setNotifyResult( arg0.getNotifyResult() );
        sysPaymentOrder.setOrderId( arg0.getOrderId() );
        sysPaymentOrder.setOrderNo( arg0.getOrderNo() );
        sysPaymentOrder.setPayTime( arg0.getPayTime() );
        sysPaymentOrder.setPayToken( arg0.getPayToken() );
        sysPaymentOrder.setPayTokenExpireTime( arg0.getPayTokenExpireTime() );
        sysPaymentOrder.setPayTokenUsed( arg0.getPayTokenUsed() );
        sysPaymentOrder.setPaymentMethod( arg0.getPaymentMethod() );
        sysPaymentOrder.setProductId( arg0.getProductId() );
        sysPaymentOrder.setProductTitle( arg0.getProductTitle() );
        sysPaymentOrder.setProductType( arg0.getProductType() );
        sysPaymentOrder.setRemark( arg0.getRemark() );
        sysPaymentOrder.setStatus( arg0.getStatus() );
        sysPaymentOrder.setUserAgent( arg0.getUserAgent() );
        sysPaymentOrder.setUserId( arg0.getUserId() );

        return sysPaymentOrder;
    }

    @Override
    public SysPaymentOrder convert(PaymentOrderVo arg0, SysPaymentOrder arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setAlipayTradeNo( arg0.getAlipayTradeNo() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setClientIp( arg0.getClientIp() );
        arg1.setExpireTime( arg0.getExpireTime() );
        arg1.setLastNotifyTime( arg0.getLastNotifyTime() );
        arg1.setNotifyCount( arg0.getNotifyCount() );
        arg1.setNotifyResult( arg0.getNotifyResult() );
        arg1.setOrderId( arg0.getOrderId() );
        arg1.setOrderNo( arg0.getOrderNo() );
        arg1.setPayTime( arg0.getPayTime() );
        arg1.setPayToken( arg0.getPayToken() );
        arg1.setPayTokenExpireTime( arg0.getPayTokenExpireTime() );
        arg1.setPayTokenUsed( arg0.getPayTokenUsed() );
        arg1.setPaymentMethod( arg0.getPaymentMethod() );
        arg1.setProductId( arg0.getProductId() );
        arg1.setProductTitle( arg0.getProductTitle() );
        arg1.setProductType( arg0.getProductType() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setUserAgent( arg0.getUserAgent() );
        arg1.setUserId( arg0.getUserId() );

        return arg1;
    }
}
