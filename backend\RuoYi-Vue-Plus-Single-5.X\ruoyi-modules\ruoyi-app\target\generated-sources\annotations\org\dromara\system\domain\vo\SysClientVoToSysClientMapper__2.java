package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__137;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysClient;
import org.dromara.system.domain.SysClientToSysClientVoMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__137.class,
    uses = {SysClientToSysClientVoMapper__2.class},
    imports = {}
)
public interface SysClientVoToSysClientMapper__2 extends BaseMapper<SysClientVo, SysClient> {
}
