package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__137;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.FeedbackBoToFeedbackMapper__2;
import org.dromara.app.domain.vo.FeedbackVo;
import org.dromara.app.domain.vo.FeedbackVoToFeedbackMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__137.class,
    uses = {FeedbackVoToFeedbackMapper__2.class,FeedbackBoToFeedbackMapper__2.class},
    imports = {}
)
public interface FeedbackToFeedbackVoMapper__2 extends BaseMapper<Feedback, FeedbackVo> {
}
