package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__137;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysUserBoToSysUserMapper__2;
import org.dromara.system.domain.vo.SysRoleVoToSysRoleMapper__2;
import org.dromara.system.domain.vo.SysUserVo;
import org.dromara.system.domain.vo.SysUserVoToSysUserMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__137.class,
    uses = {SysRoleVoToSysRoleMapper__2.class,SysRoleToSysRoleVoMapper__2.class,SysUserVoToSysUserMapper__2.class,SysUserBoToSysUserMapper__2.class},
    imports = {}
)
public interface SysUserToSysUserVoMapper__2 extends BaseMapper<SysUser, SysUserVo> {
}
