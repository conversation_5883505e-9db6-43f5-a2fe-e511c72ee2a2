package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__137;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysDictTypeBoToSysDictTypeMapper__2;
import org.dromara.system.domain.vo.SysDictTypeVo;
import org.dromara.system.domain.vo.SysDictTypeVoToSysDictTypeMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__137.class,
    uses = {SysDictTypeBoToSysDictTypeMapper__2.class,SysDictTypeVoToSysDictTypeMapper__2.class},
    imports = {}
)
public interface SysDictTypeToSysDictTypeVoMapper__2 extends BaseMapper<SysDictType, SysDictTypeVo> {
}
