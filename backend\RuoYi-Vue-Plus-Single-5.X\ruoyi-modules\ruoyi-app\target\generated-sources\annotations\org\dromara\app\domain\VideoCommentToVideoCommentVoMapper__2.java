package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__137;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.VideoCommentBoToVideoCommentMapper__2;
import org.dromara.app.domain.vo.VideoCommentVo;
import org.dromara.app.domain.vo.VideoCommentVoToVideoCommentMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__137.class,
    uses = {VideoCommentBoToVideoCommentMapper__2.class,VideoCommentVoToVideoCommentMapper__2.class},
    imports = {}
)
public interface VideoCommentToVideoCommentVoMapper__2 extends BaseMapper<VideoComment, VideoCommentVo> {
}
