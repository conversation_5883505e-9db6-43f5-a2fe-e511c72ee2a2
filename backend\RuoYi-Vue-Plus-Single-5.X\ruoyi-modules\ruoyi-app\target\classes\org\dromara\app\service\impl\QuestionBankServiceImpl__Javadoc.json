{"doc": " 题库管理Service业务层处理\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "convertToVo", "paramTypes": ["org.dromara.app.domain.QuestionBank"], "doc": " 转换QuestionBank到QuestionBankVo\n"}, {"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询题库\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.app.domain.bo.QuestionBankBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询题库列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.app.domain.bo.QuestionBankBo"], "doc": " 查询题库列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.app.domain.bo.QuestionBankBo"], "doc": " 新增题库\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.app.domain.bo.QuestionBankBo"], "doc": " 修改题库\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.app.domain.QuestionBank"], "doc": " 保存前的数据校验\n"}, {"name": "existsBankCode", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 检查题库编码是否存在\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 批量删除题库\n"}, {"name": "importBank", "paramTypes": ["java.util.List", "java.lang.Bo<PERSON>an", "java.lang.String"], "doc": " 批量导入题库\n"}, {"name": "exportBankList", "paramTypes": ["org.dromara.app.domain.bo.QuestionBankBo"], "doc": " 导出题库列表\n"}, {"name": "updateStatus", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 更新题库状态\n"}, {"name": "batchUpdateStatus", "paramTypes": ["java.util.List", "java.lang.String"], "doc": " 批量更新题库状态\n"}, {"name": "copyBank", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String"], "doc": " 复制题库\n"}, {"name": "updateTotalQuestions", "paramTypes": ["java.lang.Long"], "doc": " 更新题库题目总数\n"}, {"name": "getBankStatistics", "paramTypes": ["java.lang.Long"], "doc": " 获取题库统计信息\n"}, {"name": "batchSetSort", "paramTypes": ["java.util.List", "java.util.List"], "doc": " 批量设置题库排序\n"}], "constructors": []}