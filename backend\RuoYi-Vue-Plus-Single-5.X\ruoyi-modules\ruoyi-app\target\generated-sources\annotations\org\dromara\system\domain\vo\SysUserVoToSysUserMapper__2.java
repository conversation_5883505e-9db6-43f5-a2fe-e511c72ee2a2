package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__137;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysRoleToSysRoleVoMapper__2;
import org.dromara.system.domain.SysUser;
import org.dromara.system.domain.SysUserToSysUserVoMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__137.class,
    uses = {SysRoleVoToSysRoleMapper__2.class,SysRoleToSysRoleVoMapper__2.class,SysUserToSysUserVoMapper__2.class},
    imports = {}
)
public interface SysUserVoToSysUserMapper__2 extends BaseMapper<SysUserVo, SysUser> {
}
