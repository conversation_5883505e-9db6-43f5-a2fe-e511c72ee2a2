package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__137;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysOssBoToSysOssMapper__2;
import org.dromara.system.domain.vo.SysOssVo;
import org.dromara.system.domain.vo.SysOssVoToSysOssMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__137.class,
    uses = {SysOssBoToSysOssMapper__2.class,SysOssVoToSysOssMapper__2.class},
    imports = {}
)
public interface SysOssToSysOssVoMapper__2 extends BaseMapper<SysOss, SysOssVo> {
}
