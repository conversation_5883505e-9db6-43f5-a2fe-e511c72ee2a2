package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__137;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.VideoComment;
import org.dromara.app.domain.VideoCommentToVideoCommentVoMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__137.class,
    uses = {VideoCommentToVideoCommentVoMapper__2.class,VideoCommentToVideoCommentVoMapper__2.class},
    imports = {}
)
public interface VideoCommentVoToVideoCommentMapper__2 extends BaseMapper<VideoCommentVo, VideoComment> {
}
