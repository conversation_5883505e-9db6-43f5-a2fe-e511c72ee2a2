package org.dromara.system.domain;

import java.time.LocalDateTime;
import java.time.ZoneId;
import javax.annotation.processing.Generated;
import org.dromara.system.domain.vo.MajorVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T21:25:54+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class SysMajorToMajorVoMapper__2Impl implements SysMajorToMajorVoMapper__2 {

    @Override
    public MajorVo convert(SysMajor arg0) {
        if ( arg0 == null ) {
            return null;
        }

        MajorVo majorVo = new MajorVo();

        majorVo.setColor( arg0.getColor() );
        if ( arg0.getCreateBy() != null ) {
            majorVo.setCreateBy( String.valueOf( arg0.getCreateBy() ) );
        }
        if ( arg0.getCreateTime() != null ) {
            majorVo.setCreateTime( LocalDateTime.ofInstant( arg0.getCreateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        majorVo.setIcon( arg0.getIcon() );
        majorVo.setMajorCode( arg0.getMajorCode() );
        majorVo.setMajorId( arg0.getMajorId() );
        majorVo.setMajorName( arg0.getMajorName() );
        majorVo.setQuestionBankCount( arg0.getQuestionBankCount() );
        majorVo.setRemark( arg0.getRemark() );
        majorVo.setSort( arg0.getSort() );
        majorVo.setStatus( arg0.getStatus() );
        if ( arg0.getUpdateBy() != null ) {
            majorVo.setUpdateBy( String.valueOf( arg0.getUpdateBy() ) );
        }
        if ( arg0.getUpdateTime() != null ) {
            majorVo.setUpdateTime( LocalDateTime.ofInstant( arg0.getUpdateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }

        return majorVo;
    }

    @Override
    public MajorVo convert(SysMajor arg0, MajorVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setColor( arg0.getColor() );
        if ( arg0.getCreateBy() != null ) {
            arg1.setCreateBy( String.valueOf( arg0.getCreateBy() ) );
        }
        else {
            arg1.setCreateBy( null );
        }
        if ( arg0.getCreateTime() != null ) {
            arg1.setCreateTime( LocalDateTime.ofInstant( arg0.getCreateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        else {
            arg1.setCreateTime( null );
        }
        arg1.setIcon( arg0.getIcon() );
        arg1.setMajorCode( arg0.getMajorCode() );
        arg1.setMajorId( arg0.getMajorId() );
        arg1.setMajorName( arg0.getMajorName() );
        arg1.setQuestionBankCount( arg0.getQuestionBankCount() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setSort( arg0.getSort() );
        arg1.setStatus( arg0.getStatus() );
        if ( arg0.getUpdateBy() != null ) {
            arg1.setUpdateBy( String.valueOf( arg0.getUpdateBy() ) );
        }
        else {
            arg1.setUpdateBy( null );
        }
        if ( arg0.getUpdateTime() != null ) {
            arg1.setUpdateTime( LocalDateTime.ofInstant( arg0.getUpdateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        else {
            arg1.setUpdateTime( null );
        }

        return arg1;
    }
}
