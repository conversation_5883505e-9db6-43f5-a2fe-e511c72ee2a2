package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__137;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysPostBoToSysPostMapper__2;
import org.dromara.system.domain.vo.SysPostVo;
import org.dromara.system.domain.vo.SysPostVoToSysPostMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__137.class,
    uses = {SysPostBoToSysPostMapper__2.class,SysPostVoToSysPostMapper__2.class},
    imports = {}
)
public interface SysPostToSysPostVoMapper__2 extends BaseMapper<SysPost, SysPostVo> {
}
